#!/bin/bash

# TVP Preview CMS Docker部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|logs|build|status|clean]

set -e

# 项目名称
PROJECT_NAME="tvp-cms"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}[$1]${NC} $3"
}

print_success() {
    print_message "SUCCESS" "$GREEN" "$1"
}

print_error() {
    print_message "ERROR" "$RED" "$1"
}

print_warning() {
    print_message "WARNING" "$YELLOW" "$1"
}

print_info() {
    print_message "INFO" "$BLUE" "$1"
}

# 检查Docker和Docker Compose是否安装
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 构建镜像
build_images() {
    print_info "构建Docker镜像..."
    docker-compose build --no-cache
    print_success "镜像构建完成"
}

# 启动服务
start_services() {
    print_info "启动服务..."
    docker-compose up -d
    
    print_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        print_success "服务启动成功！"
        print_info "前端访问地址: http://localhost:8082"
        print_info "后端API地址: http://localhost:8001"
        show_status
    else
        print_error "服务启动失败，请检查日志"
        show_logs
    fi
}

# 停止服务
stop_services() {
    print_info "停止服务..."
    docker-compose down
    print_success "服务已停止"
}

# 重启服务
restart_services() {
    print_info "重启服务..."
    stop_services
    start_services
}

# 显示服务状态
show_status() {
    print_info "服务状态:"
    docker-compose ps
    
    print_info "网络状态:"
    docker network ls | grep tvp-cms || print_warning "网络未创建"
    
    print_info "数据卷状态:"
    docker volume ls | grep tvp-cms || print_warning "数据卷未创建"
}

# 显示日志
show_logs() {
    local service=${1:-""}
    
    if [ -n "$service" ]; then
        print_info "显示 $service 服务日志:"
        docker-compose logs -f --tail=50 "$service"
    else
        print_info "显示所有服务日志:"
        docker-compose logs -f --tail=50
    fi
}

# 清理资源
clean_resources() {
    print_warning "这将删除所有容器、镜像、网络和数据卷"
    read -p "确认继续吗? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "清理资源..."
        
        # 停止并删除容器
        docker-compose down -v --remove-orphans
        
        # 删除镜像
        docker images | grep tvp-cms | awk '{print $3}' | xargs -r docker rmi -f
        
        # 删除网络
        docker network rm tvp-cms-network 2>/dev/null || true
        
        # 删除数据卷
        docker volume rm tvp-cms-backend-data 2>/dev/null || true
        
        print_success "清理完成"
    else
        print_info "取消清理操作"
    fi
}

# 健康检查
health_check() {
    print_info "执行健康检查..."
    
    # 检查后端API
    if curl -f -s http://localhost:8001/api/health > /dev/null; then
        print_success "后端API健康检查通过"
    else
        print_error "后端API健康检查失败"
    fi
    
    # 检查前端
    if curl -f -s http://localhost:8082 > /dev/null; then
        print_success "前端服务健康检查通过"
    else
        print_error "前端服务健康检查失败"
    fi
}

# 显示帮助信息
show_help() {
    echo "TVP Preview CMS Docker部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [command]"
    echo ""
    echo "可用命令:"
    echo "  start    - 启动所有服务"
    echo "  stop     - 停止所有服务"
    echo "  restart  - 重启所有服务"
    echo "  build    - 构建Docker镜像"
    echo "  status   - 显示服务状态"
    echo "  logs     - 显示所有服务日志"
    echo "  logs <service> - 显示指定服务日志 (backend/frontend)"
    echo "  health   - 执行健康检查"
    echo "  clean    - 清理所有资源（危险操作）"
    echo "  help     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                 # 启动服务"
    echo "  $0 logs backend          # 查看后端日志"
    echo "  $0 restart               # 重启服务"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_dependencies
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            check_dependencies
            restart_services
            ;;
        "build")
            check_dependencies
            build_images
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "health")
            health_check
            ;;
        "clean")
            clean_resources
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 
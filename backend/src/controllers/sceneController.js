const Scene = require('../models/Scene');
const Response = require('../utils/response');
const config = require('../config');

class SceneController {
  /**
   * 获取场景列表
   */
  static async getList(ctx) {
    try {
      const { page = 1, limit = 10, project_id = '', status = null, offset = 0, name = '' } = ctx.query;
      
      // 支持offset参数（前端使用的是offset而不是page）
      let pageNum = 1;
      if (offset !== undefined) {
        const offsetNum = parseInt(offset) || 0;
        const limitNum = parseInt(limit) || 10;
        pageNum = Math.floor(offsetNum / limitNum) + 1;
      } else {
        pageNum = Math.max(1, parseInt(page) || 1);
      }
      
      const limitNum = Math.min(config.pagination.maxLimit, Math.max(1, parseInt(limit) || 10));
      
      const result = await Scene.getList({
        page: pageNum,
        limit: limitNum,
        project_id: project_id.trim(),
        status: status !== null ? parseInt(status) : null,
        name: name.trim()
      });
      
      Response.paginated(ctx, result.list, result.total, pageNum, limitNum);
    } catch (error) {
      console.error('获取场景列表失败:', error);
      Response.error(ctx, '获取场景列表失败');
    }
  }

  /**
   * 获取场景详情
   */
  static async getById(ctx) {
    try {
      const { id } = ctx.params;
      
      if (!id) {
        return Response.error(ctx, '场景ID不能为空', 400);
      }
      
      const scene = await Scene.getById(id);
      
      if (!scene) {
        return Response.error(ctx, '场景不存在', 404);
      }
      
      Response.success(ctx, scene);
    } catch (error) {
      console.error('获取场景详情失败:', error);
      Response.error(ctx, '获取场景详情失败');
    }
  }

  /**
   * 创建场景
   */
  static async create(ctx) {
    try {
      console.log('后端接收到的场景数据:', ctx.request.body);
      const { project_id, name, description, atmosphere, thumbnail_path, asset_id, metadata, status } = ctx.request.body;

      // 参数验证
      if (!project_id || !name) {
        return Response.error(ctx, '项目ID和场景名称不能为空', 400);
      }

      const sceneData = {
        project_id,
        name: name.trim(),
        description,
        atmosphere,
        thumbnail_path,
        asset_id,
        metadata,
        status
      };

      console.log('准备创建场景，数据:', sceneData);
      const scene = await Scene.create(sceneData);
      Response.success(ctx, scene, '创建场景成功');
    } catch (error) {
      console.error('创建场景失败:', error);
      Response.error(ctx, '创建场景失败');
    }
  }

  /**
   * 更新场景
   */
  static async update(ctx) {
    try {
      const { id } = ctx.params;
      const { name, description, atmosphere, thumbnail_path, asset_id, metadata, status, project_id } = ctx.request.body;

      if (!id) {
        return Response.error(ctx, '场景ID不能为空', 400);
      }

      if (!name) {
        return Response.error(ctx, '场景名称不能为空', 400);
      }

      // 检查场景是否存在
      const existingScene = await Scene.getById(id);
      if (!existingScene) {
        return Response.error(ctx, '场景不存在', 404);
      }

      const sceneData = {
        name: name.trim(),
        description,
        atmosphere,
        thumbnail_path,
        asset_id,
        metadata,
        status,
        project_id
      };
      
      const scene = await Scene.update(id, sceneData);
      Response.success(ctx, scene, '更新场景成功');
    } catch (error) {
      console.error('更新场景失败:', error);
      Response.error(ctx, '更新场景失败');
    }
  }

  /**
   * 删除场景
   */
  static async delete(ctx) {
    try {
      const { id } = ctx.params;
      
      if (!id) {
        return Response.error(ctx, '场景ID不能为空', 400);
      }
      
      // 检查场景是否存在
      const existingScene = await Scene.getById(id);
      if (!existingScene) {
        return Response.error(ctx, '场景不存在', 404);
      }
      
      const success = await Scene.delete(id);
      
      if (success) {
        Response.success(ctx, null, '删除场景成功');
      } else {
        Response.error(ctx, '删除场景失败');
      }
    } catch (error) {
      console.error('删除场景失败:', error);
      Response.error(ctx, '删除场景失败');
    }
  }

  /**
   * 批量删除场景
   */
  static async batchDelete(ctx) {
    try {
      const { ids } = ctx.request.body;
      
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return Response.error(ctx, '请选择要删除的场景', 400);
      }
      
      const deletedCount = await Scene.batchDelete(ids);
      Response.success(ctx, { deletedCount }, `成功删除 ${deletedCount} 个场景`);
    } catch (error) {
      console.error('批量删除场景失败:', error);
      Response.error(ctx, '批量删除场景失败');
    }
  }

  /**
   * 获取项目下的场景列表
   */
  static async getByProjectId(ctx) {
    try {
      const { projectId } = ctx.params;
      const { page = 1, limit = 10, status = null } = ctx.query;
      
      if (!projectId) {
        return Response.error(ctx, '项目ID不能为空', 400);
      }
      
      const pageNum = Math.max(1, parseInt(page) || 1);
      const limitNum = Math.min(config.pagination.maxLimit, Math.max(1, parseInt(limit) || 10));
      
      const result = await Scene.getByProject(projectId, {
        page: pageNum,
        limit: limitNum,
        status: status !== null ? parseInt(status) : null
      });
      
      Response.paginated(ctx, result.list, result.total, pageNum, limitNum);
    } catch (error) {
      console.error('获取项目场景列表失败:', error);
      Response.error(ctx, '获取项目场景列表失败');
    }
  }
}

module.exports = SceneController;

const { query } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Scene {
  /**
   * 获取场景列表
   * @param {Object} options - 查询选项
   * @param {number} options.page - 页码
   * @param {number} options.limit - 每页数量
   * @param {string} options.project_id - 项目ID
   * @param {number} options.status - 状态
   * @param {string} options.name - 场景名称（模糊查询）
   * @returns {Promise<{list: Array, total: number}>}
   */
  static async getList({ page = 1, limit = 10, project_id = '', status = null, name = '' }) {
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE 1=1';
    let params = [];

    if (name) {
      whereClause += ' AND s.name LIKE ?';
      params.push(`%${name}%`);
    }

    if (project_id) {
      whereClause += ' AND s.project_id = ?';
      params.push(project_id);
    }

    if (status !== null) {
      whereClause += ' AND s.status = ?';
      params.push(status);
    }
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM scenes s ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 获取列表数据，包含项目信息和关联资产信息
    const listSql = `
      SELECT
        s.id, s.project_id, s.name, s.description, s.atmosphere, s.thumbnail_path, s.asset_id, s.metadata, s.status, s.created_at, s.updated_at,
        p.name as project_name,
        a.name as asset_name,
        a.source_file_url,
        a.source_file_size
      FROM scenes s
      LEFT JOIN projects p ON s.project_id = p.id
      LEFT JOIN assets a ON s.asset_id = a.id
      ${whereClause}
      ORDER BY s.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;
    const list = await query(listSql, params);
    
    return { list, total };
  }

  /**
   * 根据ID获取场景详情
   * @param {string} id - 场景ID
   * @returns {Promise<Object|null>}
   */
  static async getById(id) {
    const sql = `
      SELECT
        s.*,
        p.name as project_name,
        a.name as asset_name,
        a.source_file_url,
        a.source_file_size
      FROM scenes s
      LEFT JOIN projects p ON s.project_id = p.id
      LEFT JOIN assets a ON s.asset_id = a.id
      WHERE s.id = ?
    `;
    const result = await query(sql, [id]);
    return result[0] || null;
  }

  /**
   * 创建场景
   * @param {Object} sceneData - 场景数据
   * @returns {Promise<Object>}
   */
  static async create(sceneData) {
    const id = uuidv4();
    const now = new Date();
    
    const sql = `
      INSERT INTO scenes (id, project_id, name, description, atmosphere, thumbnail_path, asset_id, metadata, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      sceneData.project_id,
      sceneData.name,
      sceneData.description || '',
      sceneData.atmosphere || '',
      sceneData.thumbnail_path || '',
      sceneData.asset_id || null,
      sceneData.metadata ? JSON.stringify(sceneData.metadata) : null,
      sceneData.status || 1,
      now,
      now
    ];
    
    await query(sql, params);
    return await this.getById(id);
  }

  /**
   * 更新场景
   * @param {string} id - 场景ID
   * @param {Object} sceneData - 更新数据
   * @returns {Promise<Object>}
   */
  static async update(id, sceneData) {
    const now = new Date();
    
    const sql = `
      UPDATE scenes
      SET name = ?, description = ?, atmosphere = ?, thumbnail_path = ?, asset_id = ?, metadata = ?, status = ?, project_id = ?, updated_at = ?
      WHERE id = ?
    `;

    const params = [
      sceneData.name,
      sceneData.description || '',
      sceneData.atmosphere || '',
      sceneData.thumbnail_path || '',
      sceneData.asset_id || null,
      sceneData.metadata ? JSON.stringify(sceneData.metadata) : null,
      sceneData.status,
      sceneData.project_id,
      now,
      id
    ];
    
    await query(sql, params);
    return await this.getById(id);
  }

  /**
   * 删除场景
   * @param {string} id - 场景ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    const sql = 'DELETE FROM scenes WHERE id = ?';
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  /**
   * 批量删除场景
   * @param {Array<string>} ids - 场景ID数组
   * @returns {Promise<number>}
   */
  static async batchDelete(ids) {
    if (!ids || ids.length === 0) return 0;
    
    const placeholders = ids.map(() => '?').join(',');
    const sql = `DELETE FROM scenes WHERE id IN (${placeholders})`;
    const result = await query(sql, ids);
    return result.affectedRows;
  }

  /**
   * 获取项目下的场景列表
   * @param {string} projectId - 项目ID
   * @param {Object} options - 查询选项
   * @returns {Promise<{list: Array, total: number}>}
   */
  static async getByProject(projectId, { page = 1, limit = 10, status = null }) {
    return await this.getList({ page, limit, project_id: projectId, status });
  }
}

module.exports = Scene;

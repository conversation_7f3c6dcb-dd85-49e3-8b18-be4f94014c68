const { query, transaction } = require('../config/database');
const crypto = require('crypto');

class FileService {
  /**
   * 根据文件MD5检查是否存在已完成的上传
   */
  static async checkFileExists(fileMd5, fileSize) {
    const sql = `
      SELECT file_url, file_key, file_size 
      FROM upload_tasks 
      WHERE file_md5 = ? AND file_size = ? AND status = 'completed'
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const result = await query(sql, [fileMd5, fileSize]);
    return result.length > 0 ? result[0] : null;
  }

  /**
   * 更新业务表的文件字段
   */
  static async updateBusinessTableFile(module, recordId, fileUrl, fileSize) {
    const tableMap = {
      'assets': 'assets',
      'scenes': 'scenes', 
      'project_roles': 'project_role'
    };

    const tableName = tableMap[module];
    if (!tableName) {
      throw new Error(`不支持的模块类型: ${module}`);
    }

    const sql = `
      UPDATE ${tableName} 
      SET source_file_url = ?, source_file_size = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(sql, [fileUrl, fileSize, recordId]);
  }

  /**
   * 计算文件MD5（用于服务端验证）
   */
  static calculateFileMd5(buffer) {
    return crypto.createHash('md5').update(buffer).digest('hex');
  }

  /**
   * 批量更新业务表文件字段
   */
  static async batchUpdateBusinessTableFiles(updates) {
    await transaction(async (connection) => {
      for (const update of updates) {
        const { module, recordId, fileUrl, fileSize } = update;
        
        const tableMap = {
          'assets': 'assets',
          'scenes': 'scenes',
          'project_roles': 'project_role'
        };

        const tableName = tableMap[module];
        if (tableName) {
          const sql = `
            UPDATE ${tableName} 
            SET source_file_url = ?, source_file_size = ?, updated_at = NOW()
            WHERE id = ?
          `;
          
          await connection.execute(sql, [fileUrl, fileSize, recordId]);
        }
      }
    });
  }

  /**
   * 清理无效的文件记录
   */
  static async cleanupInvalidFiles() {
    const sql = `
      UPDATE upload_tasks 
      SET status = 'failed', updated_at = NOW()
      WHERE status = 'uploading' 
      AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `;
    
    await query(sql);
  }
}

module.exports = FileService;
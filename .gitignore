# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/frontend/node_modules
/frontend/coverage

# production
/frontend/dist

# misc
.DS_Store
.idea
.VSCodeCounter

# log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# lock
yarn.lock
package-lock.json

# code editor setting
/.vscode

Dockerfile.backend
Dockerfile.frontend
docker-compose.yml
/backend/src/config/cos.js
/backend/src/config/database.js

*.md
*.mdc
node_modules
/admin-1_0_5
/bsc_core-1_1_3

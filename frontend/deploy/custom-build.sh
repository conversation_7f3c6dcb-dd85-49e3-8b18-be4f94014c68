# 在123平台的“非RPC”模式下，系统首先会去找编译镜像的Dockerfile，然后构建编译镜像
# 并且，需要在123平台配置目录为“deploy”，然后系统会自动读取deploy/custom-build.sh并执行，因此该脚本无需手动执行

echo "--- 运行custom-build.sh文件"

# 先检查是否存在package.json文件，否则说明没有拉到代码仓库
source /etc/profile
cd ../frontend
if [ -s 'package.json' ];
then 
    echo '--- package.json存在'; 
else { echo '--- 未找到package.json';  exit 1; } 
fi

# env，app，server均需要在123平台配置，配置后会以变量的形式导出，因此在这里可以直接拿到
echo "--- 当前构建env=$env, app=$app, server=$server"

sudo chmod 777 /usr/local/lib/node_modules

# 在执行到custom-build.sh时，编译镜像已经执行完毕，编译镜像里面安装了nvm和node，这里检查一下版本
node -v

# 清理 .env* 中的 NODE_ENV 定义，避免 Vite 对 .env 中 NODE_ENV 的限制报错/警告
for envfile in .env .env.*; do
  if [ -f "$envfile" ]; then
    echo "--- 清理 $envfile 中的 NODE_ENV"
    sed -i '/^NODE_ENV=/d' "$envfile"
  fi
done

# 安装依赖
echo '--- install dependencies'
npm install

# 执行构建命令
echo '--- start build'
# 避免环境变量中的 NODE_ENV 影响 Vite，按 Vite 官方建议在配置中定义
unset NODE_ENV
npm run build

# 需要把所有打包产物都移动到构建目录下，这是为了方便后续start.sh脚本操作
# 该项目采用nextjs打包，打包后有4个产物，分别是dist文件夹，public文件夹，node_modules文件夹，package.json文件
echo '--- 移动打包产物到./deploy'
ls -a
# mv dist public node_modules package.json app.js ./deploy
cp -r dist public node_modules package.json app.js ./deploy
# 检查是否移动成功
cd ./deploy
echo '--- 进入deploy文件夹'
ls -a

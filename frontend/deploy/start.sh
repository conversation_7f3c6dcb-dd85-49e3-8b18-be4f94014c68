# 123平台的"非RPC"编译环境，要求当前的构建目录下必须有start.sh,stop.sh,monitor.sh三个脚本文件
# 构建的执行顺序为：构建编译镜像 -> 执行custom-build.sh -> 构建运行镜像，之后自动执行start.sh脚本文件
echo "--- 运行start.sh脚本"

# 加载环境变量
source /etc/profile

# main_port在123平台"服务详情" -> "端口信息"中配置的，是平台自动分配，这里可直接获取该变量
echo "main_port=$main_port"

# 设置node环境变量
#export PATH="/home/<USER>/versions/node/v18.20.4/bin:$PATH"
export PATH="/usr/local/bin:/home/<USER>/versions/node/v18.20.4/bin:$PATH"
# 设置工作目录
bashPath=/usr/local/app
binPath=/usr/local/application/bin

# 123平台会将构建目录deploy目录内除了三个必须脚本外的所有文件，都移动到/usr/local/application/bin
cd $bashPath

# 把打包产物移动到工作目录
cp -r $binPath/dist $binPath/public $binPath/node_modules $binPath/package.json $binPath/app.js $bashPath

# 判断是否移动成功
cd $bashPath
if [ -d 'dist' ] && [ -d 'public' ] && [ -d 'node_modules' ] && [ -f 'package.json' ];
then
    echo '--- 打包产物dist复制成功'
    echo '--- 打包产物node_modules复制成功'
    echo '--- 打包产物public文件夹复制成功'
    echo '--- 打包产物package.json文件夹复制成功'
else
    echo '--- 未找到上述打包产物'
    exit 1;
fi

# 设置应用端口
mainPort=${main_port}
echo "应用将在端口 ${mainPort} 上运行"

# 启动应用、node服务
#echo "检查node环境："
#which node || echo "node命令未找到"
#which npm || echo "npm命令未找到"

cd /usr/local/app

# 检查是否存在app.js文件（Express服务器）
if [ -f "app.js" ]; then
    echo "找到app.js，使用Express服务器启动"
    SERVER_FILE="app.js"
else
    echo "未找到app.js，创建简单的静态文件服务器"
    
    # 检查是否安装了express
    if ! npm list express &>/dev/null; then
        echo "Express未安装，正在安装..."
        npm install express
    fi
    
    # 创建ES模块兼容的静态文件服务器
    cat > simple-server.js << 'EOF'
import express from 'express';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();

console.log('Starting static file server...');
console.log('Serving files from:', join(__dirname, 'dist'));

// 服务静态文件
app.use(express.static('dist'));

// 处理SPA路由
app.get('*', (req, res) => {
  res.sendFile(join(__dirname, 'dist', 'index.html'));
});

const port = process.env.PORT;
app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
  console.log('Static files served from dist/ directory');
});
EOF
    SERVER_FILE="simple-server.js"
fi

echo "准备启动服务器文件: $SERVER_FILE"

# 检查dist目录是否存在
if [ ! -d "dist" ]; then
    echo "错误: dist目录不存在，请检查构建过程"
    exit 1
fi



# 检查是否需要安装PM2
if ! npm list pm2 &>/dev/null; then
    echo "PM2未安装，正在本地安装..."
    npm install pm2
fi

# 使用本地PM2启动
echo "使用PM2启动服务器..."
echo "设置环境变量 PORT=${mainPort}"

# 启动应用时设置更严格的重启策略
PORT=${mainPort} npx pm2 start $SERVER_FILE \
    --name "dam-frontend" \
    --max-restarts 3 \
    --restart-delay 5000

npx pm2 save

echo "服务器启动成功"

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查进程启动情况
echo "=== 进程状态检查 ==="

echo "PM2进程状态："
npx pm2 status

# 验证端口是否正常监听
echo "验证端口监听状态..."
RETRY_COUNT=0
MAX_RETRIES=2

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if lsof -i :${mainPort} >/dev/null 2>&1; then
        echo "✅ ${mainPort}端口正常监听"
        echo "监听进程信息："
        lsof -i :${mainPort}
        break
    else
        echo "⏳ 端口 ${mainPort} 尚未监听，等待中... (${RETRY_COUNT}/${MAX_RETRIES})"
        sleep 2
        RETRY_COUNT=$((RETRY_COUNT + 1))
    fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo "❌ ${mainPort}端口启动失败，检查PM2日志："
    npx pm2 logs dam-frontend --lines 10
    echo "=== 启动失败 ==="
    exit 1
fi

echo "=== 进程状态检查结束 ==="

exit 0

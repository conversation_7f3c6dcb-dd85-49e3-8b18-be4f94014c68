#!/bin/bash
# 123平台要求的monitor.sh脚本

echo "--- 运行monitor.sh脚本"

# 检查nginx状态
echo "=== 检查nginx状态 ==="
nginx_count=$(ps -ef | grep nginx | grep -v grep | wc -l)
if [ $nginx_count -gt 0 ]; then
    echo "✅ nginx: 运行中 ($nginx_count 个进程)"
else
    echo "❌ nginx: 未运行"
fi

# 检查端口监听状态
echo "=== 检查端口状态 ==="
if netstat -tuln | grep ":${main_port:-8080}" >/dev/null 2>&1; then
    echo "✅ 主端口 ${main_port:-8080}: 监听中"
else
    echo "❌ 主端口 ${main_port:-8080}: 未监听"
fi

if lsof -i :3000 >/dev/null 2>&1; then
    echo "✅ Node.js端口 3000: 监听中"
    echo "监听进程信息："
    lsof -i :3000
else
    echo "❌ Node.js端口 3000: 未监听"
fi

# 检查PM2进程
echo "=== 检查PM2进程 ==="
npx pm2 status dam-frontend 2>/dev/null || echo "❌ PM2进程不存在"

echo "=== 监控检查完成 ==="
exit 0

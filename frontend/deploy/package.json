{"name": "material-tailwind-dashboard-react", "private": true, "version": "2.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "npx tsc --noEmit"}, "dependencies": {"@heroicons/react": "2.0.18", "@material-tailwind/react": "2.1.4", "antd": "^5.24.2", "apexcharts": "3.44.0", "express": "^4.21.2", "pm2": "^6.0.8", "prop-types": "15.8.1", "react": "18.2.0", "react-apexcharts": "1.4.1", "react-dom": "18.2.0", "react-router-dom": "6.17.0", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20.11.30", "@types/react": "18.2.31", "@types/react-dom": "18.2.14", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "4.1.0", "autoprefixer": "10.4.16", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "8.4.31", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "0.5.6", "tailwindcss": "3.3.4", "typescript": "^5.8.2", "vite": "4.5.0"}}
user  root;
worker_processes  1;

# 创建日志目录的指令应该在脚本中处理，这里只设置基本的错误日志
error_log  /usr/local/app/logs/error.log;

pid        /usr/local/app/logs/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    
    # access_log 应该在 http 块内
    access_log  /usr/local/app/logs/access.log;
    
    # 设置临时目录
    client_body_temp_path /usr/local/app/tmp/client_body;
    proxy_temp_path /usr/local/app/tmp/proxy;
    fastcgi_temp_path /usr/local/app/tmp/fastcgi;
    uwsgi_temp_path /usr/local/app/tmp/uwsgi;
    scgi_temp_path /usr/local/app/tmp/scgi;
    
    server {
        listen       ${MAIN_PORT};
        large_client_header_buffers 4 32k;
		
        # 对于Vite React项目，所有请求都转发给Node.js服务器
        location / {
                proxy_pass http://localhost:3000;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 如果要直接服务静态文件（备用方案）
        # location / {
        #     root /usr/local/app/dist;
        #     try_files $uri $uri/ /index.html;
        # }
    }
}
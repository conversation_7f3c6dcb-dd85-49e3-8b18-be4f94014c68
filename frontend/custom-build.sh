#!/bin/sh

################################################################################################################################
# 自定义编译脚本是 123 平台在标准编译流程之外提供的扩展能力。
# 1、tRPC 提供的标准编译流程，已经覆盖了绝大多数的业务场景，建议业务先评估确有特殊原因标准流程无法处理，且不属于通用型的解决方案，再采用自定义编译脚本的方式
# 2、标准编译流程，一样可以支持自定义构件，详细参考文档 https://iwiki.woa.com/pages/viewpage.action?pageId=188800415
# 3、如果你评估还是认为需要使用自定义脚本，建议在标准的编译命令 `deploy-node` 之后再去做进一步处理。如果不采用该命令，完全自己完成编译流程，需要符合平台的发布包规范，否则会发布失败
# 4、自定义脚本属于比较高阶的用法，建议开发同学对流程比较熟悉之后再使用自定义脚本，否则容易走弯路
################################################################################################################################

# 开始编译之前，编译机会准备好 `input`、`output` 两个文件夹，编译源码（也就是业务代码）放置在 input 目录下
# ├── input
# └── output

# 建议先执行标准的编译命令 `deploy-node`
# deploy-node（本命令会完成基础的编译，也会自动安装项目的依赖，如果需要安装 devDependencies，请加参数 `--with-devdeps`）
# deploy-node 的详细用法请参考：https://git.woa.com/trpc-node/platform/deploy-node

deploy-node input output

if [ $? -ne 0 ]
then
  echo "deploy-node task failed"
  exit -1
fi

# 如果 deploy-node 成功执行，则 `output` 目录如下：
# 其中 `bin/src` 目录为源码所在目录，一般而言应该在该目录下继续自定义构建行为
# ├── output
# │   ├── bin
# │   │   ├── node            // 当前编译镜像 Node 版本的可执行文件
# │   │   ├── node-agent-trpc // 适用于 123 平台的服务 agent
# │   │   └── src             // 业务源码，包含已经安装的依赖
# │   ├── conf                // 空文件夹，为符合平台发布包规范要求
# │   └── data                // 空文件夹，为符合平台发布包规范要求
# │   ├── log                 // 空文件夹，为符合平台发布包规范要求
# │   └── script              // 空文件夹，为符合平台发布包规范要求

# 【重要提示】请在 `output/bin/src` 目录下，继续完成自定义的构建行为，不要操作 input 目录，尤其是不要在 input 中安装依赖
customBuildDemo() {
  ls -a
  echo "custom build start"
  cd $1
  ls -a
  npm install --legacy-peer-deps;
  npm run build;
  return $?;
}
customBuildDemo "output/bin/src"
if [ $? -ne 0 ]
then
  echo "customBuild task failed"
  exit -2
fi

# success
exit 0;

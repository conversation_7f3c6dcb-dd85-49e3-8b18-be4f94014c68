import React, { useState, useEffect } from 'react';
import {
  Dialog,
  Input,
  Button,
  Space,
  Card,
  Image,
  Tag,
  Pagination,
  Empty,
  Loading,
  message,
} from 'tdesign-react';
import { SearchIcon, CheckIcon, BrowseIcon } from 'tdesign-icons-react';
import { AssetService, Asset } from '../../services/assets';
import { buildThumbnailUrl } from '../../utils/url';
import './index.module.less';

interface AssetSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (asset: Asset) => void;
  selectedAssetId?: string;
}

const AssetSelector: React.FC<AssetSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  selectedAssetId,
}) => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailAsset, setDetailAsset] = useState<Asset | null>(null);

  const pageSize = 12; // 每页显示12个资产

  // 获取场景模型资产列表
  const fetchAssets = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const params: any = {
        offset,
        limit: pageSize,
        type_id: 2, // 场景模型类型ID
      };

      if (searchValue) {
        params.name = searchValue;
      }

      const response = await AssetService.list(params);
      setAssets(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取资产列表失败');
      console.error('获取资产列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchAssets();
    }
  }, [visible, currentPage, searchValue]);

  // 重置状态
  useEffect(() => {
    if (visible) {
      setCurrentPage(1);
      setSearchValue('');
      // 不重置选择的资产，保持已选择状态
    }
  }, [visible]);

  // 初始化选择状态
  useEffect(() => {
    if (visible && selectedAssetId) {
      // 如果传入了已选择的资产ID，查找对应的资产对象
      const findSelectedAsset = async () => {
        try {
          const response = await AssetService.list({
            type_id: 2,
            limit: 100
          });
          const asset = response.data.find((item: Asset) => item.id === selectedAssetId);
          if (asset) {
            setSelectedAsset(asset);
          }
        } catch (error) {
          console.error('查找选中资产失败:', error);
        }
      };
      findSelectedAsset();
    } else if (visible && !selectedAssetId) {
      setSelectedAsset(null);
    }
  }, [visible, selectedAssetId]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 选择资产
  const handleSelectAsset = (asset: Asset) => {
    console.log('handleSelectAsset called with:', asset.name, asset.id);
    console.log('Current selectedAsset:', selectedAsset?.name, selectedAsset?.id);

    // 如果点击的是已选中的资产，则取消选择
    if (selectedAsset?.id === asset.id) {
      console.log('取消选择资产:', asset.name);
      setSelectedAsset(null);
    } else {
      console.log('选择资产:', asset.name);
      setSelectedAsset(asset);
    }
  };

  // 确认选择
  const handleConfirm = () => {
    if (selectedAsset) {
      onSelect(selectedAsset);
      onClose();
    }
  };

  // 查看详情
  const handleViewDetail = (asset: Asset, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发选择
    setDetailAsset(asset);
    setDetailVisible(true);
  };

  // 格式化文件大小
  const formatFileSize = (size?: number) => {
    if (!size) return '-';
    if (size < 1024) return `${size}B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)}MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)}GB`;
  };

  return (
    <Dialog
      header="选择场景资产"
      visible={visible}
      onClose={onClose}
      width={900}
      height={700}
      closeBtn={true}
      closeOnEscKeydown={true}
      closeOnOverlayClick={false}
      footer={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button 
            theme="primary" 
            onClick={handleConfirm}
            disabled={!selectedAsset}
          >
            确认选择
          </Button>
        </Space>
      }
    >
      <div style={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
        {/* 搜索栏 */}
        <div style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Input
              placeholder="搜索场景资产名称"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '300px' }}
              suffixIcon={<SearchIcon />}
            />
            <div style={{ fontSize: '12px', color: '#666' }}>
              点击资产卡片选择，再次点击取消选择
            </div>
          </div>
        </div>

        {/* 资产列表 */}
        <div style={{ flex: 1, overflow: 'auto' }}>
          {loading ? (
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '400px' 
            }}>
              <Loading text="加载中..." />
            </div>
          ) : assets.length === 0 ? (
            <Empty description="暂无场景资产" />
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
              gap: '16px',
              padding: '8px'
            }}>
              {assets.map((asset) => (
                <div
                  key={asset.id}
                  style={{
                    cursor: 'pointer',
                    border: selectedAsset?.id === asset.id ? '2px solid #0052d9' : '1px solid #e7e7e7',
                    backgroundColor: selectedAsset?.id === asset.id ? '#f0f8ff' : '#fff',
                    position: 'relative',
                    transition: 'all 0.2s ease',
                    boxShadow: selectedAsset?.id === asset.id ? '0 4px 12px rgba(0, 82, 217, 0.2)' : undefined,
                    borderRadius: '6px',
                    padding: '16px',
                    minHeight: '200px'
                  }}
                  onClick={() => {
                    console.log('Card clicked, asset:', asset.name, asset.id);
                    handleSelectAsset(asset);
                  }}
                  onMouseEnter={(e) => {
                    if (selectedAsset?.id !== asset.id) {
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedAsset?.id !== asset.id) {
                      e.currentTarget.style.boxShadow = 'none';
                    }
                  }}
                >
                  {/* 选中标识 */}
                  {selectedAsset?.id === asset.id && (
                    <div style={{
                      position: 'absolute',
                      top: '8px',
                      right: '8px',
                      backgroundColor: '#0052d9',
                      borderRadius: '50%',
                      width: '24px',
                      height: '24px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 1
                    }}>
                      <CheckIcon size="16px" style={{ color: 'white' }} />
                    </div>
                  )}

                  {/* 缩略图 */}
                  <div style={{ 
                    height: '120px', 
                    overflow: 'hidden',
                    borderRadius: '4px',
                    backgroundColor: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: '8px'
                  }}>
                    {asset.thumbnail_path ? (
                      <Image
                        src={buildThumbnailUrl(asset.thumbnail_path)}
                        style={{ 
                          width: '100%', 
                          height: '100%', 
                          objectFit: 'cover' 
                        }}
                        fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNCA0IDI4IDIwIDIwIDIwQzEyIDIwIDE2IDQgMjAgMjhaIiBmaWxsPSIjQ0NDQ0NDIi8+Cjwvc3ZnPgo="
                      />
                    ) : (
                      <div style={{ 
                        color: '#ccc', 
                        fontSize: '12px',
                        textAlign: 'center'
                      }}>
                        暂无预览
                      </div>
                    )}
                  </div>

                  {/* 资产信息 */}
                  <div>
                    <div style={{
                      fontSize: '14px',
                      fontWeight: 500,
                      color: '#333',
                      marginBottom: '4px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }} title={asset.name}>
                      {asset.name}
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '8px'
                    }}>
                      <Tag size="small" variant="light">
                        场景模型
                      </Tag>
                      <span style={{ fontSize: '12px', color: '#666' }}>
                        {formatFileSize(asset.source_file_size)}
                      </span>
                    </div>

                    {/* 查看详情按钮 */}
                    <div style={{ textAlign: 'center' }}>
                      <Button
                        size="small"
                        variant="text"
                        onClick={(e) => handleViewDetail(asset, e)}
                        style={{
                          fontSize: '12px',
                          padding: '2px 8px',
                          height: '24px'
                        }}
                      >
                        <BrowseIcon size="14px" style={{ marginRight: '4px' }} />
                        查看详情
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 分页 */}
        {total > pageSize && (
          <div style={{ 
            marginTop: '16px', 
            display: 'flex', 
            justifyContent: 'center' 
          }}>
            <Pagination
              current={currentPage}
              total={total}
              pageSize={pageSize}
              onChange={(pageInfo) => setCurrentPage(pageInfo.current)}
              showJumper
              showTotal
            />
          </div>
        )}
      </div>

      {/* 资产详情弹窗 */}
      <Dialog
        header="资产详情"
        visible={detailVisible}
        onClose={() => setDetailVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setDetailVisible(false)}>关闭</Button>
            {detailAsset && (
              <Button
                theme={selectedAsset?.id === detailAsset.id ? "default" : "primary"}
                onClick={() => {
                  handleSelectAsset(detailAsset);
                  setDetailVisible(false);
                }}
              >
                {selectedAsset?.id === detailAsset.id ? "取消选择" : "选择此资产"}
              </Button>
            )}
          </Space>
        }
      >
        {detailAsset && (
          <div style={{ padding: '16px 0' }}>
            {/* 缩略图 */}
            <div style={{
              textAlign: 'center',
              marginBottom: '20px'
            }}>
              {detailAsset.thumbnail_path ? (
                <Image
                  src={buildThumbnailUrl(detailAsset.thumbnail_path)}
                  style={{
                    maxWidth: '300px',
                    maxHeight: '200px',
                    borderRadius: '8px'
                  }}
                />
              ) : (
                <div style={{
                  width: '300px',
                  height: '200px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto',
                  color: '#999'
                }}>
                  暂无预览图
                </div>
              )}
            </div>

            {/* 详细信息 */}
            <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
              <div style={{ marginBottom: '12px' }}>
                <strong>资产名称：</strong>
                <span>{detailAsset.name}</span>
              </div>

              <div style={{ marginBottom: '12px' }}>
                <strong>资产类型：</strong>
                <Tag size="small" variant="light" style={{ marginLeft: '8px' }}>
                  场景模型
                </Tag>
              </div>

              <div style={{ marginBottom: '12px' }}>
                <strong>文件大小：</strong>
                <span>{formatFileSize(detailAsset.source_file_size)}</span>
              </div>

              <div style={{ marginBottom: '12px' }}>
                <strong>创建时间：</strong>
                <span>{new Date(detailAsset.created_at).toLocaleString()}</span>
              </div>

              <div style={{ marginBottom: '12px' }}>
                <strong>更新时间：</strong>
                <span>{new Date(detailAsset.updated_at).toLocaleString()}</span>
              </div>

              {detailAsset.metadata && (
                <div style={{ marginBottom: '12px' }}>
                  <strong>元数据：</strong>
                  <div style={{
                    marginTop: '8px',
                    padding: '12px',
                    backgroundColor: '#f8f8f8',
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontFamily: 'monospace'
                  }}>
                    {typeof detailAsset.metadata === 'string'
                      ? detailAsset.metadata
                      : JSON.stringify(detailAsset.metadata, null, 2)
                    }
                  </div>
                </div>
              )}

              <div style={{ marginBottom: '12px' }}>
                <strong>状态：</strong>
                <Tag
                  size="small"
                  theme={detailAsset.status === 1 ? 'success' : 'default'}
                  style={{ marginLeft: '8px' }}
                >
                  {detailAsset.status === 1 ? '可用' : '不可用'}
                </Tag>
              </div>
            </div>
          </div>
        )}
      </Dialog>
    </Dialog>
  );
};

export default AssetSelector;

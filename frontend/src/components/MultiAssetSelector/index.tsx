import React, { useState, useEffect } from 'react';
import {
  Dialog,
  Input,
  Button,
  Space,
  Image,
  Tag,
  Pagination,
  Empty,
  Loading,
  message,
  Card,
} from 'tdesign-react';
import { SearchIcon, CheckIcon, BrowseIcon, CloseIcon } from 'tdesign-icons-react';
import { AssetService, Asset } from '../../services/assets';
import { buildThumbnailUrl } from '../../utils/url';

interface MultiAssetSelectorProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (assets: Asset[]) => void;
  selectedAssets?: Asset[];
  assetTypeId?: number; // 资产类型ID，默认为1（角色模型）
}

const MultiAssetSelector: React.FC<MultiAssetSelectorProps> = ({
  visible,
  onClose,
  onConfirm,
  selectedAssets = [],
  assetTypeId = 1, // 默认为角色模型
}) => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [tempSelectedAssets, setTempSelectedAssets] = useState<Asset[]>([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailAsset, setDetailAsset] = useState<Asset | null>(null);

  const pageSize = 12; // 每页显示12个资产

  // 初始化临时选择的资产
  useEffect(() => {
    if (visible) {
      setTempSelectedAssets([...selectedAssets]);
      setCurrentPage(1);
      setSearchValue('');
    }
  }, [visible, selectedAssets]);

  // 获取角色模型资产列表
  const fetchAssets = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const params: any = {
        offset,
        limit: pageSize,
        type_id: assetTypeId, // 角色模型类型
      };

      if (searchValue.trim()) {
        params.name = searchValue.trim();
      }

      const response = await AssetService.list(params);
      setAssets(response.data || []);
      setTotal(response.pagination?.total || 0);
    } catch (error) {
      console.error('获取资产列表失败:', error);
      message.error('获取资产列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 页面变化时重新获取数据
  useEffect(() => {
    if (visible) {
      fetchAssets();
    }
  }, [visible, currentPage, searchValue, assetTypeId]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 切换资产选择状态
  const handleToggleAsset = (asset: Asset) => {
    const isSelected = tempSelectedAssets.some(item => item.id === asset.id);
    if (isSelected) {
      // 移除选择
      setTempSelectedAssets(prev => prev.filter(item => item.id !== asset.id));
    } else {
      // 添加选择
      setTempSelectedAssets(prev => [...prev, asset]);
    }
  };

  // 移除已选择的资产
  const handleRemoveAsset = (assetId: string) => {
    setTempSelectedAssets(prev => prev.filter(item => item.id !== assetId));
  };

  // 确认选择
  const handleConfirm = () => {
    onConfirm(tempSelectedAssets);
    onClose();
  };

  // 查看详情
  const handleViewDetail = (asset: Asset, event: React.MouseEvent) => {
    event.stopPropagation();
    setDetailAsset(asset);
    setDetailVisible(true);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      <Dialog
        header="选择角色资产"
        visible={visible}
        onClose={onClose}
        width={1000}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={false}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button theme="primary" onClick={handleConfirm}>
              确认选择 ({tempSelectedAssets.length})
            </Button>
          </Space>
        }
      >
        <div style={{ padding: '16px 0' }}>
          {/* 搜索栏 */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Input
                placeholder="搜索角色资产名称"
                value={searchValue}
                onChange={(value) => setSearchValue(value)}
                onEnter={() => handleSearch(searchValue)}
                style={{ width: '300px' }}
                suffixIcon={<SearchIcon />}
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                点击资产卡片选择，再次点击取消选择
              </div>
            </div>
          </div>

          {/* 已选择的资产 */}
          {tempSelectedAssets.length > 0 && (
            <div style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px', fontWeight: 500 }}>
                已选择 ({tempSelectedAssets.length})：
              </div>
              <div style={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: '8px',
                padding: '12px',
                backgroundColor: '#f8f9fa',
                borderRadius: '6px',
                maxHeight: '120px',
                overflowY: 'auto'
              }}>
                {tempSelectedAssets.map(asset => (
                  <Tag
                    key={asset.id}
                    closable
                    onClose={() => handleRemoveAsset(asset.id)}
                    style={{ marginBottom: '4px' }}
                  >
                    {asset.name}
                  </Tag>
                ))}
              </div>
            </div>
          )}

          {/* 资产网格 */}
          <Loading loading={loading}>
            {assets.length === 0 ? (
              <Empty description="暂无角色资产" />
            ) : (
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                gap: '16px',
                marginBottom: '16px'
              }}>
                {assets.map(asset => {
                  const isSelected = tempSelectedAssets.some(item => item.id === asset.id);
                  return (
                    <div
                      key={asset.id}
                      style={{
                        cursor: 'pointer',
                        border: isSelected ? '2px solid #0052d9' : '1px solid #e7e7e7',
                        backgroundColor: isSelected ? '#f0f8ff' : '#fff',
                        position: 'relative',
                        transition: 'all 0.2s ease',
                        boxShadow: isSelected ? '0 4px 12px rgba(0, 82, 217, 0.2)' : undefined,
                        borderRadius: '6px',
                        padding: '16px',
                        minHeight: '200px'
                      }}
                      onClick={() => handleToggleAsset(asset)}
                      onMouseEnter={(e) => {
                        if (!isSelected) {
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isSelected) {
                          e.currentTarget.style.boxShadow = 'none';
                        }
                      }}
                    >
                      {/* 选中标识 */}
                      {isSelected && (
                        <div style={{
                          position: 'absolute',
                          top: '8px',
                          right: '8px',
                          width: '24px',
                          height: '24px',
                          backgroundColor: '#0052d9',
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          zIndex: 1
                        }}>
                          <CheckIcon size="14px" style={{ color: '#fff' }} />
                        </div>
                      )}

                      {/* 缩略图 */}
                      <div style={{ 
                        textAlign: 'center', 
                        marginBottom: '12px' 
                      }}>
                        {asset.thumbnail_path ? (
                          <Image
                            src={buildThumbnailUrl(asset.thumbnail_path)}
                            style={{ 
                              width: '100%', 
                              height: '120px', 
                              objectFit: 'cover',
                              borderRadius: '4px'
                            }}
                          />
                        ) : (
                          <div style={{
                            width: '100%',
                            height: '120px',
                            backgroundColor: '#f5f5f5',
                            borderRadius: '4px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#999'
                          }}>
                            暂无预览图
                          </div>
                        )}
                      </div>

                      {/* 资产信息 */}
                      <div style={{ fontSize: '14px' }}>
                        <div style={{ 
                          fontWeight: 500, 
                          marginBottom: '8px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {asset.name}
                        </div>
                        
                        <div style={{ 
                          display: 'flex', 
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <Tag size="small" variant="light">
                            角色模型
                          </Tag>
                          <span style={{ fontSize: '12px', color: '#666' }}>
                            {formatFileSize(asset.source_file_size)}
                          </span>
                        </div>
                        
                        {/* 查看详情按钮 */}
                        <div style={{ textAlign: 'center' }}>
                          <Button
                            size="small"
                            variant="text"
                            onClick={(e) => handleViewDetail(asset, e)}
                            style={{ 
                              fontSize: '12px',
                              padding: '2px 8px',
                              height: '24px'
                            }}
                          >
                            <BrowseIcon size="14px" style={{ marginRight: '4px' }} />
                            查看详情
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </Loading>

          {/* 分页 */}
          {total > pageSize && (
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                onChange={(pageInfo) => {
                  setCurrentPage(pageInfo.current);
                }}
                showJumper
                showSizer={false}
              />
            </div>
          )}
        </div>
      </Dialog>

      {/* 资产详情弹窗 */}
      <Dialog
        header="资产详情"
        visible={detailVisible}
        onClose={() => setDetailVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setDetailVisible(false)}>关闭</Button>
            {detailAsset && (
              <Button 
                theme={tempSelectedAssets.some(item => item.id === detailAsset.id) ? "default" : "primary"}
                onClick={() => {
                  if (detailAsset) {
                    handleToggleAsset(detailAsset);
                    setDetailVisible(false);
                  }
                }}
              >
                {tempSelectedAssets.some(item => item.id === detailAsset.id) ? "取消选择" : "选择此资产"}
              </Button>
            )}
          </Space>
        }
      >
        {detailAsset && (
          <div style={{ padding: '16px 0' }}>
            {/* 缩略图 */}
            <div style={{ 
              textAlign: 'center', 
              marginBottom: '20px' 
            }}>
              {detailAsset.thumbnail_path ? (
                <Image
                  src={buildThumbnailUrl(detailAsset.thumbnail_path)}
                  style={{ 
                    maxWidth: '300px',
                    maxHeight: '200px',
                    borderRadius: '8px'
                  }}
                />
              ) : (
                <div style={{
                  width: '300px',
                  height: '200px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto',
                  color: '#999'
                }}>
                  暂无预览图
                </div>
              )}
            </div>

            {/* 详细信息 */}
            <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
              <div style={{ marginBottom: '12px' }}>
                <strong>资产名称：</strong>
                <span>{detailAsset.name}</span>
              </div>
              
              <div style={{ marginBottom: '12px' }}>
                <strong>资产类型：</strong>
                <Tag size="small" variant="light" style={{ marginLeft: '8px' }}>
                  角色模型
                </Tag>
              </div>
              
              <div style={{ marginBottom: '12px' }}>
                <strong>文件大小：</strong>
                <span>{formatFileSize(detailAsset.source_file_size)}</span>
              </div>
              
              <div style={{ marginBottom: '12px' }}>
                <strong>创建时间：</strong>
                <span>{new Date(detailAsset.created_at).toLocaleString()}</span>
              </div>
              
              <div style={{ marginBottom: '12px' }}>
                <strong>更新时间：</strong>
                <span>{new Date(detailAsset.updated_at).toLocaleString()}</span>
              </div>
              
              {detailAsset.metadata && (
                <div style={{ marginBottom: '12px' }}>
                  <strong>元数据：</strong>
                  <div style={{ 
                    marginTop: '8px',
                    padding: '12px',
                    backgroundColor: '#f8f8f8',
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontFamily: 'monospace'
                  }}>
                    {typeof detailAsset.metadata === 'string' 
                      ? detailAsset.metadata 
                      : JSON.stringify(detailAsset.metadata, null, 2)
                    }
                  </div>
                </div>
              )}
              
              <div style={{ marginBottom: '12px' }}>
                <strong>状态：</strong>
                <Tag 
                  size="small" 
                  theme={detailAsset.status === 1 ? 'success' : 'default'}
                  style={{ marginLeft: '8px' }}
                >
                  {detailAsset.status === 1 ? '可用' : '不可用'}
                </Tag>
              </div>
            </div>
          </div>
        )}
      </Dialog>
    </>
  );
};

export default MultiAssetSelector;

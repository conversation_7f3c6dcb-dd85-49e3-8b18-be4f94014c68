import React, { useState, useEffect } from 'react';
import { buildThumbnailUrl } from '../../../utils/url';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
  Image,
} from 'tdesign-react';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon, BrowseIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';
import AssetSelector from '../../../components/AssetSelector';
import SourceFileManager from '../../../components/SourceFileManager';
import { SceneService, Scene, SceneCreateData, SceneUpdateData } from '../../../services/scenes';
import { ProjectService, Project } from '../../../services/projects';
import { AssetService, Asset } from '../../../services/assets';
import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const ScenesPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  const [loading, setLoading] = useState(false);
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [projectFilter, setProjectFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<number | undefined>();

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingScene, setEditingScene] = useState<Scene | null>(null);
  const [form] = Form.useForm();

  // 监听字段值变化
  const thumbnailPath = Form.useWatch('thumbnail_path', form);

  // 查看弹窗状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingScene, setViewingScene] = useState<Scene | null>(null);

  // 资产选择相关状态
  const [assetSelectorVisible, setAssetSelectorVisible] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);

  // 状态选项
  const statusOptions = [
    { value: 1, label: '规划中' },
    { value: 2, label: '制作中' },
    { value: 3, label: '已完成' },
    { value: 4, label: '已废弃' },
  ];

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await ProjectService.getList({ limit: 100 });
      setProjects(response.list || []);
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };

  // 获取场景列表
  const fetchScenes = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const params: any = {
        offset,
        limit: pageSize,
      };

      if (searchValue) {
        params.name = searchValue;
      }
      if (projectFilter) {
        params.project_id = projectFilter;
      }
      if (statusFilter !== undefined) {
        params.status = statusFilter;
      }

      const response = await SceneService.list(params);
      setScenes(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取场景列表失败');
      console.error('获取场景列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    fetchScenes();
  }, [currentPage, pageSize, searchValue, projectFilter, statusFilter]);

  // 移除重复的表单值设置逻辑，避免与handleEdit中的设置冲突

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setProjectFilter('');
    setStatusFilter(undefined);
    setCurrentPage(1);
  };

  // 打开资产选择器
  const handleOpenAssetSelector = () => {
    setAssetSelectorVisible(true);
  };

  // 选择资产
  const handleSelectAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    form.setFieldsValue({
      scene_file: asset.id,
      asset_id: asset.id,
      asset_name: asset.name,
      source_file_url: asset.source_file_url,
      source_file_size: asset.source_file_size,
    });
  };

  // 清除选择的资产
  const handleClearAsset = () => {
    setSelectedAsset(null);
    form.setFieldsValue({
      scene_file: '',
      asset_id: '',
      asset_name: '',
      source_file_url: '',
      source_file_size: 0,
    });
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingScene(null);
    setSelectedAsset(null);
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        status: 1,
      });
    }, 0);
    setModalVisible(true);
  };

  // 打开查看弹窗
  const handleView = (scene: Scene) => {
    setViewingScene(scene);
    setViewModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = async (scene: Scene) => {
    setModalType('edit');
    setEditingScene(scene);
    form.reset();

    // 如果场景有关联的资产ID，尝试查找对应的资产
    if (scene.asset_id) {
      try {
        const response = await AssetService.list({
          type_id: 2, // 场景模型类型
          limit: 100
        });
        const matchedAsset = response.data.find((asset: Asset) =>
          asset.id === scene.asset_id
        );
        if (matchedAsset) {
          setSelectedAsset(matchedAsset);
        }
      } catch (error) {
        console.error('查找关联资产失败:', error);
      }
    } else {
      setSelectedAsset(null);
    }

    setTimeout(() => {
      form.setFieldsValue({
        name: scene.name,
        project_id: scene.project_id,
        description: scene.description,
        atmosphere: scene.atmosphere,
        status: scene.status,
        thumbnail_path: scene.thumbnail_path,
        scene_file: scene.asset_id ? 'selected' : '',
      });
    }, 0);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        // 处理元数据
        let metadata = null;
        if (values.metadata && values.metadata.trim()) {
          try {
            metadata = JSON.parse(values.metadata);
          } catch (e) {
            message.error('元数据格式不正确，请输入有效的JSON格式');
            return;
          }
        }

        let recordId: string;

        if (modalType === 'create') {
          console.log('创建场景 - selectedAsset:', selectedAsset);
          console.log('创建场景 - values:', values);

          const createData: SceneCreateData = {
            project_id: values.project_id,
            name: values.name,
            description: values.description || '',
            atmosphere: values.atmosphere || '',
            thumbnail_path: values.thumbnail_path || '',
            asset_id: selectedAsset?.id || undefined,
            metadata: metadata,
          };

          console.log('创建场景 - createData:', createData);
          const createdScene = await SceneService.create(createData);
          recordId = createdScene.id;
          message.success('创建场景成功');
        } else {
          const updateData: SceneUpdateData = {
            project_id: values.project_id,
            name: values.name,
            description: values.description,
            atmosphere: values.atmosphere,
            status: values.status,
            thumbnail_path: values.thumbnail_path,
            asset_id: selectedAsset?.id || editingScene?.asset_id || undefined,
            metadata: metadata,
          };
          console.log('更新场景 - updateData:', updateData);
          await SceneService.update(editingScene!.id, updateData);
          recordId = editingScene!.id;
          message.success('更新场景成功');
        }

        // 场景创建成功，无需额外处理文件上传任务

        setModalVisible(false);
        fetchScenes();
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建场景失败' : '更新场景失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除场景
  const handleDelete = async (id: string) => {
    try {
      await SceneService.delete(id);
      message.success('删除场景成功');
      fetchScenes();
    } catch (error) {
      message.error('删除场景失败');
      console.error('删除场景失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的场景');
      return;
    }

    try {
      await SceneService.batchDelete(selectedRowKeys);
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      fetchScenes();
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 获取项目名称
  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project?.name || projectId;
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '缩略图',
      colKey: 'thumbnail_path',
      width: 80,
      cell: ({ row }: { row: Scene }) => {
        const imageUrl = row.thumbnail_path
          ? (buildThumbnailUrl(row.thumbnail_path))
          : '/images/default-thumbnail.svg';
        return (
          <Image
            src={imageUrl}
            style={{ width: '60px', height: '45px', borderRadius: '4px' }}
            fit="cover"
            error={
              <div style={{
                width: '60px',
                height: '45px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#999'
              }}>
                无图片
              </div>
            }
          />
        );
      },
    },
    {
      title: '场景名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '所属项目',
      colKey: 'id',
      width: 150,
      cell: ({ row }: { row: Scene }) => getProjectName(row.project_id),
    },
    {
      title: '描述',
      colKey: 'description',
      width: 250,
      cell: ({ row }: { row: Scene }) => row.description || '-',
    },
    {
      title: '氛围',
      colKey: 'atmosphere',
      width: 120,
      cell: ({ row }: { row: Scene }) => row.atmosphere || '-',
    },
    {
      title: '状态',
      colKey: 'status',
      width: 100,
      cell: ({ row }: { row: Scene }) => {
        const statusMap = {
          1: { label: '规划中', color: 'warning' },
          2: { label: '制作中', color: 'primary' },
          3: { label: '已完成', color: 'success' },
          4: { label: '已废弃', color: 'default' },
        };
        const statusInfo = statusMap[row.status as keyof typeof statusMap] || { label: '未知', color: 'default' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Scene }) => dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 200,
      cell: ({ row }: { row: Scene }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => handleView(row)}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这个场景吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索场景名称"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '200px' }}
              suffixIcon={<SearchIcon />}
            />
            <Select
              placeholder="选择项目"
              value={projectFilter}
              onChange={(value) => setProjectFilter(value as string)}
              clearable
              style={{ width: '150px' }}
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
            <Select
              placeholder="状态"
              value={statusFilter}
              onChange={(value) => setStatusFilter(value as number)}
              clearable
              style={{ width: '100px' }}
            >
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            <Button onClick={() => handleSearch(searchValue)} icon={<SearchIcon />}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" onClick={handleCreate} icon={<AddIcon />}>
              新建场景
            </Button>
            {selectedRowKeys.length === 0 ? (
              <Button
                theme="danger"
                disabled={true}
                icon={<DeleteIcon />}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            ) : (
              <Popconfirm
                content={`确定要删除选中的 ${selectedRowKeys.length} 个场景吗？`}
                onConfirm={handleBatchDelete}
              >
                <Button
                  theme="danger"
                  icon={<DeleteIcon />}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={scenes}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建场景' : '编辑场景'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onClose={() => setModalVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              {modalType === 'create' ? '创建' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="场景名称"
            name="name"
            rules={[{ required: true, message: '请输入场景名称' }]}
          >
            <Input placeholder="请输入场景名称" />
          </FormItem>
          
          <FormItem
            label="所属项目"
            name="project_id"
            rules={[{ required: true, message: '请选择所属项目' }]}
          >
            <Select placeholder="请选择所属项目">
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
          </FormItem>
          
          <FormItem label="场景描述" name="description">
            <Textarea placeholder="请输入场景描述" rows={3} />
          </FormItem>
          
          <FormItem label="场景氛围" name="atmosphere">
            <Input placeholder="请输入场景氛围，如：温馨、紧张、神秘等" />
          </FormItem>
          
          <FormItem label="缩略图" name="thumbnail_path">
            <ThumbnailUpload
              value={thumbnailPath as string}
              onChange={(value) => form.setFieldsValue({ thumbnail_path: value })}
            />
          </FormItem>

          <FormItem
            label="场景文件管理"
            name="scene_file"
            rules={modalType === 'create' ? [{ required: true, message: '请选择场景资产' }] : []}
          >
            <div style={{
              border: '1px solid #e7e7e7',
              borderRadius: '6px',
              padding: '16px',
              backgroundColor: '#fff'
            }}>
              {selectedAsset ? (
                <div>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '12px'
                  }}>
                    <div style={{ flex: 1 }}>
                      <div style={{
                        fontSize: '14px',
                        fontWeight: 500,
                        marginBottom: '4px'
                      }}>
                        {selectedAsset.name}
                      </div>
                      <Tag size="small" variant="light">场景模型</Tag>
                    </div>
                    {selectedAsset.thumbnail_path && (
                      <Image
                        src={buildThumbnailUrl(selectedAsset.thumbnail_path)}
                        style={{
                          width: '60px',
                          height: '45px',
                          borderRadius: '4px',
                          marginLeft: '12px'
                        }}
                      />
                    )}
                  </div>
                  <Space>
                    <Button size="small" onClick={handleOpenAssetSelector}>
                      更换资产
                    </Button>
                    <Button size="small" variant="text" onClick={handleClearAsset}>
                      清除
                    </Button>
                  </Space>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <div style={{
                    color: '#999',
                    marginBottom: '12px',
                    fontSize: '14px'
                  }}>
                    请选择场景资产
                  </div>
                  <Button onClick={handleOpenAssetSelector}>
                    选择场景资产
                  </Button>
                </div>
              )}
            </div>
          </FormItem>

          <FormItem label="元数据" name="metadata">
            <Textarea
              placeholder="请输入JSON格式的元数据，例如：{&quot;key&quot;: &quot;value&quot;}"
              rows={4}
              onBlur={(value) => {
                // 验证JSON格式
                if (value && value.trim()) {
                  try {
                    JSON.parse(value);
                  } catch (e) {
                    message.error('元数据格式不正确，请输入有效的JSON格式');
                  }
                }
              }}
            />
          </FormItem>

          <FormItem label="状态" name="status">
            <Select placeholder="请选择状态">
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </FormItem>
        </Form>
      </Dialog>

      {/* 查看详情弹窗 */}
      <Dialog
        header="场景详情"
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        onClose={() => setViewModalVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Button onClick={() => setViewModalVisible(false)}>关闭</Button>
        }
      >
        {viewingScene && (
          <div style={{ padding: '16px 0' }}>
            <div style={{ marginBottom: '16px' }}>
              <strong>场景名称：</strong>
              <span>{viewingScene.name}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>所属项目：</strong>
              <span>{getProjectName(viewingScene.project_id)}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>场景描述：</strong>
              <span>{viewingScene.description || '-'}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>氛围设定：</strong>
              <span>{viewingScene.atmosphere || '-'}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>缩略图：</strong>
              <div style={{ marginTop: '8px' }}>
                {viewingScene.thumbnail_path ? (
                  <Image
                    src={buildThumbnailUrl(viewingScene.thumbnail_path)}
                    style={{ width: '120px', height: '90px', borderRadius: '4px' }}
                    fit="cover"
                    error={
                      <div style={{
                        width: '120px',
                        height: '90px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px',
                        fontSize: '12px',
                        color: '#999'
                      }}>
                        图片加载失败
                      </div>
                    }
                  />
                ) : (
                  <div style={{
                    width: '120px',
                    height: '90px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: '#999'
                  }}>
                    暂无缩略图
                  </div>
                )}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>关联资产：</strong>
              {viewingScene.source_file_url ? (
                <div style={{ marginTop: '8px' }}>
                  <SourceFileManager
                    value={{
                      url: viewingScene.source_file_url,
                      size: viewingScene.source_file_size
                    }}
                    module="scenes"
                    readOnly={true}
                    onChange={() => {}}
                  />
                </div>
              ) : (
                <span style={{ color: '#999' }}>未关联场景资产</span>
              )}
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>元数据：</strong>
              <div style={{ marginTop: '8px' }}>
                {viewingScene.metadata ? (
                  <pre style={{
                    backgroundColor: '#f5f5f5',
                    padding: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    maxHeight: '200px',
                    overflow: 'auto',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all'
                  }}>
                    {typeof viewingScene.metadata === 'string'
                      ? JSON.stringify(JSON.parse(viewingScene.metadata), null, 2)
                      : JSON.stringify(viewingScene.metadata, null, 2)
                    }
                  </pre>
                ) : (
                  <span style={{ color: '#999' }}>暂无元数据</span>
                )}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>状态：</strong>
              <span>
                {(() => {
                  const statusMap = {
                    1: '规划中',
                    2: '制作中',
                    3: '已完成',
                    4: '已废弃',
                  };
                  return statusMap[viewingScene.status as keyof typeof statusMap] || '未知';
                })()}
              </span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>创建时间：</strong>
              <span>{dayjs(viewingScene.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
            <div>
              <strong>更新时间：</strong>
              <span>{dayjs(viewingScene.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          </div>
        )}
      </Dialog>

      {/* 资产选择弹窗 */}
      <AssetSelector
        visible={assetSelectorVisible}
        onClose={() => setAssetSelectorVisible(false)}
        onSelect={handleSelectAsset}
        selectedAssetId={selectedAsset?.id}
      />
    </div>
  );
};

export default ScenesPage;

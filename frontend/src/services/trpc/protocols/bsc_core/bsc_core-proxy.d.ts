/// <reference types="node" />
/// <reference types="node" />
/**
 * generated by @tencent/trpc-tools-codec@0.8.4
 * MD5 (bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto) = c117e913d0bae2cbc95c1228cdcb99cd
 */
import Long from 'long';
import { Client, GetProxyOptions, InvokeProperty, Response } from '@tencent/trpc-rpc-client';
import { trpc } from './bsc_core';
type NoUndefinedField<T> = {
    [P in keyof T]-?: T[P] extends (infer I)[] | null | undefined ? NoUndefinedField<I>[] : T[P] extends number | Long | string | boolean | Uint8Array | Buffer | Function | Record<string, number> | null | undefined ? NonNullable<T[P]> : NoUndefinedField<T[P]> | null;
};
/**
 * > null
 *
 * `trpc.video_media.bsc_core.BscCore` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
 */
export declare class BscCoreProxy {
    static readonly fullName = "trpc.video_media.bsc_core.BscCore";
    private worker;
    constructor(client: Client);
    constructor(client: Client, objectName: string);
    constructor(client: Client, options: GetProxyOptions);
    constructor(client: Client, objectName: string, options: GetProxyOptions);
    destroy(): void;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetProjectList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetProjectList(req: trpc.video_media.bsc_core.IGetProjectListRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetProjectListResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetProject(req: trpc.video_media.bsc_core.IGetProjectRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetProjectResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SearchProjectList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    SearchProjectList(req: trpc.video_media.bsc_core.ISearchProjectListRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.SearchProjectListResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.UpdateProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    UpdateProject(req: trpc.video_media.bsc_core.IUpdateProjectRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.UpdateProjectResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.DeleteProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    DeleteProject(req: trpc.video_media.bsc_core.IDeleteProjectRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.DeleteProjectResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.CreateProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    CreateProject(req: trpc.video_media.bsc_core.ICreateProjectRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.CreateProjectResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetSceneList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetSceneList(req: trpc.video_media.bsc_core.IGetSceneListRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetSceneListResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetScene(req: trpc.video_media.bsc_core.IGetSceneRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetSceneResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SearchSceneList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    SearchSceneList(req: trpc.video_media.bsc_core.ISearchSceneListRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.SearchSceneListResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.UpdateScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    UpdateScene(req: trpc.video_media.bsc_core.IUpdateSceneRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.UpdateSceneResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.DeleteScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    DeleteScene(req: trpc.video_media.bsc_core.IDeleteSceneRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.DeleteSceneResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.CreateScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    CreateScene(req: trpc.video_media.bsc_core.ICreateSceneRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.CreateSceneResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetFilmSessionList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetFilmSessionList(req: trpc.video_media.bsc_core.IGetFilmSessionListRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetFilmSessionListResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetFilmSession` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetFilmSession(req: trpc.video_media.bsc_core.IGetFilmSessionRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetFilmSessionResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SearchFilmSessionList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    SearchFilmSessionList(req: trpc.video_media.bsc_core.ISearchFilmSessionListRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.SearchFilmSessionListResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.CreateFilmSession` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    CreateFilmSession(req: trpc.video_media.bsc_core.ICreateFilmSessionRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.CreateFilmSessionResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SaveFilmSession` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    SaveFilmSession(req: trpc.video_media.bsc_core.ISaveFilmSessionRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.SaveFilmSessionResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.FilmSessionShare` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    FilmSessionShare(req: trpc.video_media.bsc_core.IFilmSessionShareRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.FilmSessionShareResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.FilmSessionCopy` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    FilmSessionCopy(req: trpc.video_media.bsc_core.IFilmSessionCopyRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.FilmSessionCopyResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetFilmSessionHistory` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetFilmSessionHistory(req: trpc.video_media.bsc_core.IGetFilmSessionHistoryRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetFilmSessionHistoryResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetAssetsList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    GetAssetsList(req: trpc.video_media.bsc_core.IGetAssetsListRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.bsc_core.GetAssetsListResponse>>>;
}
export {};

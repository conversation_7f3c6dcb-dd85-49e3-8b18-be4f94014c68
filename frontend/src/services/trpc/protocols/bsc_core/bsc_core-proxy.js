"use strict";
/* eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BscCoreProxy = void 0;
const trpc_rpc_protocol_1 = require("@tencent/trpc-rpc-protocol");
const bsc_core_1 = require("./bsc_core");
const TRPC_PROTO_ENCODE = trpc_rpc_protocol_1.trpc.TrpcContentEncodeType.TRPC_PROTO_ENCODE;
const TRPC_INVOKE_UNKNOWN_ERR = trpc_rpc_protocol_1.trpc.TrpcRetCode.TRPC_INVOKE_UNKNOWN_ERR;
const TRPC_ONEWAY_CALL = trpc_rpc_protocol_1.trpc.TrpcCallType.TRPC_ONEWAY_CALL;
/** RpcError 转化为 RpcResponse */
function rpcError({ request, error }) {
    return {
        request,
        error,
    };
}
/**
 * > null
 *
 * `trpc.video_media.bsc_core.BscCore` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
 */
class BscCoreProxy {
    constructor(client, ...args) {
        let objectName, options;
        const [a, b] = args;
        if (typeof a === 'string') {
            objectName = a;
            options = b !== null && b !== void 0 ? b : Object.create(null);
        }
        else {
            objectName = BscCoreProxy.fullName;
            options = a !== null && a !== void 0 ? a : Object.create(null);
        }
        this.worker = client.createObjectProxy(objectName, options);
    }
    destroy() {
        this.worker.destroy();
    }
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetProjectList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetProjectList(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetProjectListRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetProjectList', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetProjectListResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetProjectList
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetProject(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetProjectRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetProject', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetProjectResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetProject
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SearchProjectList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async SearchProjectList(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.SearchProjectListRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/SearchProjectList', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.SearchProjectListResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method SearchProjectList
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.UpdateProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async UpdateProject(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.UpdateProjectRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/UpdateProject', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.UpdateProjectResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method UpdateProject
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.DeleteProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async DeleteProject(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.DeleteProjectRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/DeleteProject', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.DeleteProjectResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method DeleteProject
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.CreateProject` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async CreateProject(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.CreateProjectRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/CreateProject', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.CreateProjectResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method CreateProject
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetSceneList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetSceneList(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetSceneListRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetSceneList', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetSceneListResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetSceneList
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetScene(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetSceneRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetScene', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetSceneResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetScene
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SearchSceneList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async SearchSceneList(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.SearchSceneListRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/SearchSceneList', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.SearchSceneListResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method SearchSceneList
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.UpdateScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async UpdateScene(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.UpdateSceneRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/UpdateScene', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.UpdateSceneResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method UpdateScene
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.DeleteScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async DeleteScene(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.DeleteSceneRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/DeleteScene', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.DeleteSceneResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method DeleteScene
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.CreateScene` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async CreateScene(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.CreateSceneRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/CreateScene', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.CreateSceneResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method CreateScene
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetFilmSessionList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetFilmSessionList(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetFilmSessionListRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetFilmSessionList', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetFilmSessionListResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetFilmSessionList
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetFilmSession` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetFilmSession(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetFilmSessionRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetFilmSession', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetFilmSessionResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetFilmSession
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SearchFilmSessionList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async SearchFilmSessionList(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.SearchFilmSessionListRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/SearchFilmSessionList', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.SearchFilmSessionListResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method SearchFilmSessionList
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.CreateFilmSession` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async CreateFilmSession(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.CreateFilmSessionRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/CreateFilmSession', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.CreateFilmSessionResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method CreateFilmSession
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.SaveFilmSession` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async SaveFilmSession(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.SaveFilmSessionRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/SaveFilmSession', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.SaveFilmSessionResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method SaveFilmSession
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.FilmSessionShare` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async FilmSessionShare(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.FilmSessionShareRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/FilmSessionShare', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.FilmSessionShareResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method FilmSessionShare
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.FilmSessionCopy` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async FilmSessionCopy(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.FilmSessionCopyRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/FilmSessionCopy', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.FilmSessionCopyResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method FilmSessionCopy
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetFilmSessionHistory` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetFilmSessionHistory(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetFilmSessionHistoryRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetFilmSessionHistory', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetFilmSessionHistoryResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetFilmSessionHistory
    /**
     * > null
     *
     * `trpc.video_media.bsc_core.BscCore.GetAssetsList` defined in `bsc_core-1_1_3/video_media/bsc_core/bsc_core.proto`
     */
    async GetAssetsList(req, property = {}) {
        var _a, _b;
        const Req = bsc_core_1.trpc.video_media.bsc_core.GetAssetsListRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.bsc_core.BscCore/GetAssetsList', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = bsc_core_1.trpc.video_media.bsc_core.GetAssetsListResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetAssetsList
} // end of service BscCoreProxy
exports.BscCoreProxy = BscCoreProxy;
BscCoreProxy.fullName = 'trpc.video_media.bsc_core.BscCore';

import { instance } from '../utils/request';

// 场景类型定义
export interface Scene {
  id: string;
  project_id: string;
  name: string;
  description: string;
  atmosphere: string;
  thumbnail_path: string;
  asset_id?: string;
  metadata: string;
  status: number;
  created_at: string;
  updated_at: string;
  project_name?: string;
  asset_name?: string;
  source_file_url?: string;
  source_file_size?: number;
}

// 创建场景请求类型
export interface SceneCreateData {
  project_id: string;
  name: string;
  description?: string;
  atmosphere?: string;
  thumbnail_path?: string;
  asset_id?: string;
  metadata?: any;
}

// 更新场景请求类型
export interface SceneUpdateData {
  project_id?: string;
  name?: string;
  description?: string;
  atmosphere?: string;
  thumbnail_path?: string;
  asset_id?: string;
  status?: number;
  metadata?: any;
}

// 场景服务类
export class SceneService {
  // 获取场景列表
  static async list(params: {
    project_id?: string;
    offset?: number;
    limit?: number;
    status?: number;
    name?: string;
  } = {}) {
    const { offset = 0, limit = 10, ...filters } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/scenes?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 获取场景详情
  static async getById(id: string) {
    const response = await instance.get(`/scenes/${id}`);
    return response.data;
  }

  // 创建场景
  static async create(data: SceneCreateData) {
    const response = await instance.post('/scenes', data);
    return response.data;
  }

  // 更新场景
  static async update(id: string, data: SceneUpdateData) {
    const response = await instance.put(`/scenes/${id}`, data);
    return response.data;
  }

  // 删除场景
  static async delete(id: string) {
    const response = await instance.delete(`/scenes/${id}`);
    return response.data;
  }

  // 获取项目下的场景列表
  static async getByProject(projectId: string, params: {
    offset?: number;
    limit?: number;
    status?: number;
  } = {}) {
    const { offset = 0, limit = 10, status } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(status !== undefined && { status: status.toString() }),
    });

    const response = await instance.get(`/projects/${projectId}/scenes?${queryParams}`);
    return response.data;
  }

  // 批量删除场景
  static async batchDelete(ids: string[]) {
    const response = await instance.post('/scenes/batch-delete', { ids });
    return response.data;
  }
}

export default SceneService;

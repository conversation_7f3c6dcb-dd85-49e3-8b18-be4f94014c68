import axios from 'axios';
import proxy from '../configs/host';

const env = import.meta.env.MODE || 'development';

// 添加fallback逻辑，确保即使env不匹配也有默认值
const getApiHost = () => {
  const envConfig = proxy[env];
  if (envConfig && envConfig.API) {
    return envConfig.API;
  }
  
  // 如果当前环境配置不存在，根据运行环境决定fallback
  // 使用typeof window检查是否在浏览器环境
  if (typeof window !== 'undefined' && window.location && window.location.port === '8082') {
    return '/api';
  }
  
  // 开发环境fallback
  return 'http://localhost:3002/api';
};

const API_HOST = getApiHost();

const SUCCESS_CODE = 0;
const TIMEOUT = 30000;

export const instance = axios.create({
  baseURL: API_HOST,
  timeout: TIMEOUT,
  withCredentials: true,
});

instance.interceptors.response.use(
  // eslint-disable-next-line consistent-return
  (response) => {
    if (response.status === 200) {
      const { data } = response;
      if (data.code === SUCCESS_CODE) {
        return data;
      }
      return Promise.reject(data);
    }
    return Promise.reject(response?.data);
  },
  (e) => Promise.reject(e),
);

export default instance;

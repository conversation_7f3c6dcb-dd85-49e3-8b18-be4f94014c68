/// <reference types="node" />
/// <reference types="node" />
/**
 * generated by @tencent/trpc-tools-codec@0.8.4
 * MD5 (/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto) = 738f274b18dc975398263b47d3f55a15
 */
import Long from 'long';
import { Client, GetProxyOptions, InvokeProperty, Response } from '@tencent/trpc-rpc-client';
import { trpc } from './admin';
type NoUndefinedField<T> = {
    [P in keyof T]-?: T[P] extends (infer I)[] | null | undefined ? NoUndefinedField<I>[] : T[P] extends number | Long | string | boolean | Uint8Array | Buffer | Function | Record<string, number> | null | undefined ? NonNullable<T[P]> : NoUndefinedField<T[P]> | null;
};
/**
 * > null
 *
 * `trpc.video_media.cms_admin.AssetService` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
 */
export declare class AssetServiceProxy {
    static readonly fullName = "trpc.video_media.cms_admin.AssetService";
    private worker;
    constructor(client: Client);
    constructor(client: Client, objectName: string);
    constructor(client: Client, options: GetProxyOptions);
    constructor(client: Client, objectName: string, options: GetProxyOptions);
    destroy(): void;
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.StartUpload` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    StartUpload(req: trpc.video_media.cms_admin.IStartUploadRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.cms_admin.StartUploadResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.FinishUpload` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    FinishUpload(req: trpc.video_media.cms_admin.IFinishUploadRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.cms_admin.FinishUploadResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.CreateAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    CreateAsset(req: trpc.video_media.cms_admin.ICreateAssetRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.cms_admin.CreateAssetResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.GetAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    GetAsset(req: trpc.video_media.cms_admin.IGetAssetRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.cms_admin.GetAssetResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.UpdateAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    UpdateAsset(req: trpc.video_media.cms_admin.IUpdateAssetRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.cms_admin.UpdateAssetResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.DeleteAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    DeleteAsset(req: trpc.video_media.cms_admin.IDeleteAssetRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.cms_admin.DeleteAssetResponse>>>;
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.ListAssets` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    ListAssets(req: trpc.video_media.cms_admin.IListAssetsRequest, property?: Partial<InvokeProperty>): Promise<Response<NoUndefinedField<trpc.video_media.cms_admin.ListAssetsResponse>>>;
}
export {};

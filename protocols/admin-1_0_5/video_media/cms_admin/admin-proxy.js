"use strict";
/* eslint-disable */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetServiceProxy = void 0;
const trpc_rpc_protocol_1 = require("@tencent/trpc-rpc-protocol");
const admin_1 = require("./admin");
const TRPC_PROTO_ENCODE = trpc_rpc_protocol_1.trpc.TrpcContentEncodeType.TRPC_PROTO_ENCODE;
const TRPC_INVOKE_UNKNOWN_ERR = trpc_rpc_protocol_1.trpc.TrpcRetCode.TRPC_INVOKE_UNKNOWN_ERR;
const TRPC_ONEWAY_CALL = trpc_rpc_protocol_1.trpc.TrpcCallType.TRPC_ONEWAY_CALL;
/** RpcError 转化为 RpcResponse */
function rpcError({ request, error }) {
    return {
        request,
        error,
    };
}
/**
 * > null
 *
 * `trpc.video_media.cms_admin.AssetService` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
 */
class AssetServiceProxy {
    constructor(client, ...args) {
        let objectName, options;
        const [a, b] = args;
        if (typeof a === 'string') {
            objectName = a;
            options = b !== null && b !== void 0 ? b : Object.create(null);
        }
        else {
            objectName = AssetServiceProxy.fullName;
            options = a !== null && a !== void 0 ? a : Object.create(null);
        }
        this.worker = client.createObjectProxy(objectName, options);
    }
    destroy() {
        this.worker.destroy();
    }
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.StartUpload` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    async StartUpload(req, property = {}) {
        var _a, _b;
        const Req = admin_1.trpc.video_media.cms_admin.StartUploadRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.cms_admin.AssetService/StartUpload', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = admin_1.trpc.video_media.cms_admin.StartUploadResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method StartUpload
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.FinishUpload` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    async FinishUpload(req, property = {}) {
        var _a, _b;
        const Req = admin_1.trpc.video_media.cms_admin.FinishUploadRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.cms_admin.AssetService/FinishUpload', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = admin_1.trpc.video_media.cms_admin.FinishUploadResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method FinishUpload
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.CreateAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    async CreateAsset(req, property = {}) {
        var _a, _b;
        const Req = admin_1.trpc.video_media.cms_admin.CreateAssetRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.cms_admin.AssetService/CreateAsset', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = admin_1.trpc.video_media.cms_admin.CreateAssetResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method CreateAsset
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.GetAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    async GetAsset(req, property = {}) {
        var _a, _b;
        const Req = admin_1.trpc.video_media.cms_admin.GetAssetRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.cms_admin.AssetService/GetAsset', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = admin_1.trpc.video_media.cms_admin.GetAssetResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method GetAsset
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.UpdateAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    async UpdateAsset(req, property = {}) {
        var _a, _b;
        const Req = admin_1.trpc.video_media.cms_admin.UpdateAssetRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.cms_admin.AssetService/UpdateAsset', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = admin_1.trpc.video_media.cms_admin.UpdateAssetResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method UpdateAsset
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.DeleteAsset` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    async DeleteAsset(req, property = {}) {
        var _a, _b;
        const Req = admin_1.trpc.video_media.cms_admin.DeleteAssetRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.cms_admin.AssetService/DeleteAsset', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = admin_1.trpc.video_media.cms_admin.DeleteAssetResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method DeleteAsset
    /**
     * > null
     *
     * `trpc.video_media.cms_admin.AssetService.ListAssets` defined in `/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/video_media/cms_admin/admin.proto`
     */
    async ListAssets(req, property = {}) {
        var _a, _b;
        const Req = admin_1.trpc.video_media.cms_admin.ListAssetsRequest;
        const { request, error, response, context } = await this.worker.rpcInvoke('/trpc.video_media.cms_admin.AssetService/ListAssets', {
            type: TRPC_PROTO_ENCODE,
            data: Req.encode(Req.fromObject(req)).finish()
        }, property).catch(rpcError);
        const retCode = error == null ? 0 : (_a = error.code) !== null && _a !== void 0 ? _a : TRPC_INVOKE_UNKNOWN_ERR; // 框架错误
        const res = { retCode, costTime: (_b = request === null || request === void 0 ? void 0 : request.costTime) !== null && _b !== void 0 ? _b : 0, request, error };
        if (response == null || property.callType === TRPC_ONEWAY_CALL) {
            return res;
        }
        const { err, data, type } = response;
        res.error = err; // 业务错误
        res.buffer = data; // 业务数据
        res.type = type; // 业务数据编码类型
        // JSON 编码
        if (res.type === 2) {
            if (typeof data !== 'undefined') {
                res.response = JSON.parse(data.toString()); // 业务数据
            }
        }
        else {
            res.response = admin_1.trpc.video_media.cms_admin.ListAssetsResponse.decode(data); // 业务数据
        }
        res.context = context; // 返回的 trans info
        return res;
    } // end of method ListAssets
} // end of service AssetServiceProxy
exports.AssetServiceProxy = AssetServiceProxy;
AssetServiceProxy.fullName = 'trpc.video_media.cms_admin.AssetService';

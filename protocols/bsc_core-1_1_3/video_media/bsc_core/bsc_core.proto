syntax = "proto3"; 
//package code generated by <PERSON>  DO NOT EDIT.
package   trpc.video_media.bsc_core;

//code generated by <PERSON>  DO NOT EDIT.
option  java_package="com.tencent.trpcprotocol.video_media.bsc_core.bsc_core";
option  java_multiple_files = false; 
//java_outer_classname的命名不要与message、service、enum的命名相同
option  java_outer_classname  = "BscCorePB"; 
//code generated by Rick  DO NOT EDIT.
option  go_package ="git.woa.com/trpcprotocol/video_media/bsc_core";

// 建议使用谷歌protobuf规范 遵循PB语法检查 
// 谷歌protobuf规范地址：https://developers.google.com/protocol-buffers/docs/style
// Proto格式检查（Tencent）包含：Google protobuf规范和数据校验检查 

// 不建议使用 google/protobuf/any.proto 
// any强依赖package type.googleapis.com/_packagename_._messagename_.   
// https://developers.google.com/protocol-buffers/docs/proto3#any 

// tRPC-Go数据校验模块（**移除注释使用**） 
// tsecstr仅允许中英文字母、数字、=、+、/、@、#、_、-传入。注意，字符集不包括空格、|等符号，如有需要，请自定义校验表达式。
// 详参见规则手册：https://iwiki.woa.com/pages/viewpage.action?pageId=241919333  
import "/root/projects/TVPPrevizCMS/bsc_core-1_1_3/src/trpc/common/validate.proto";  

message CommonRsp{
    int32 code = 1;
    string msg = 2;
}


message Pagination {
  int32 page = 1;
  int32 page_size = 2;
}

enum Sort{
    DESC = 0;
    ASC = 1;
}

enum FilterOperation{
    EQUAL = 0;
    IN = 1;
    BETWEEN = 2;
    LESS = 3;
    MORE = 4;
}

message Filter{
    string field = 1;
    FilterOperation operation = 2;
    string value = 3; // 如果是多个值，用,进行分割，多个值场景是between和in，其中beween只能传 2个值
}

// 通用分页返回结构
message PageInfo {
  int32 total = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// ========== Project ==========
message Project {
  string id = 1;
  string name = 2;
  string description = 3;
}


message GetProjectListRequest {
    repeated string project_list = 1;
}

message GetProjectListResponse {
  repeated Project projects = 1;
  CommonRsp rsp = 2;
}

message GetProjectRequest{
    string project = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
}

message GetProjectResponse{
    Project project = 1;
    CommonRsp rsp = 2;
}

message SearchProjectListRequest{
    repeated Filter filters = 1;
    Pagination pagination = 2;
    Sort sort = 3;    
}


message SearchProjectListResponse{
    Project project = 1;
    CommonRsp rsp = 2;   
}
message CreateProjectRequest{
    string name = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
    string desc = 2[(validate.rules).string = {tsecstr : true}];
}

message CreateProjectResponse{
    string project_id = 1;
    CommonRsp rsp = 2;
}

message UpdateProjectRequest{
    string project_id = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
    string name = 2[(validate.rules).string = {tsecstr : true}];
    string desc = 3[(validate.rules).string = {tsecstr : true}];
}

message UpdateProjectResponse{
    CommonRsp rsp = 1;
}

message DeleteProjectRequest{
    string project_id = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
}

message DeleteProjectResponse{
    CommonRsp rsp = 1;
}

// ========== Scene & Session ==========

message Scene {
  string id = 1;
  string name = 2;
  repeated FilmSession sessions = 3;
}


message GetSceneRequest{
    string scene_id = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
}

message GetSceneResponse{
    Scene scene = 1;
    CommonRsp rsp = 2;
}

message GetSceneListRequest {
  repeated string scene_id_list = 1;
}

message GetSceneListResponse {
  repeated Scene scenes = 1;
  PageInfo page_info = 2;
}

message SearchSceneListRequest{
  Pagination pagination = 1;
  repeated Filter filters = 2;
  Sort sort = 3;
}

message SearchSceneListResponse{
  repeated Scene scenes = 1;
  PageInfo page_info = 2;
}
message CreateSceneRequest{
    string name = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
    string desc = 2[(validate.rules).string = {tsecstr : true}];
    repeated string asset_list = 3;
}

message CreateSceneResponse{
  string scene_id = 1;
  CommonRsp rsp = 2; 
}

message UpdateSceneRequest{
    string scene_id = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
    string name = 2[(validate.rules).string = {tsecstr : true}];
    string desc = 3[(validate.rules).string = {tsecstr : true}];
    repeated string asset_list = 4; 
}

message UpdateSceneResponse{
    CommonRsp rsp = 1;
}

message DeleteSceneRequest{
    string scene_id = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
}

message DeleteSceneResponse{
    CommonRsp rsp = 1;
}


message FilmSession {
  string id = 1;
  string scene_id = 2;
  string name = 3;
  string data = 4; // JSON 字符串
  int32 version = 5;
}



message GetFilmSessionRequest {
  string session_id = 1[(validate.rules).string.tsecstr = true];
}

message GetFilmSessionResponse {
  FilmSession session = 1;
}

message SearchFilmSessionListRequest{
  Pagination pagination = 1;
  repeated Filter filters = 2;
  Sort sort = 3;    
}

message SearchFilmSessionListResponse{
    repeated FilmSession film_sesion_list = 1;
    CommonRsp rsp = 2;  
}

message GetFilmSessionListRequest{
  repeated string file_session_list = 1;
}


message GetFilmSessionListResponse{
    repeated FilmSession film_sesion_list = 1;
    CommonRsp rsp = 2;
}

message CreateFilmSessionRequest {
  string scene_id = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
  string name = 2[(validate.rules).string.tsecstr = true];
  string data = 3[(validate.rules).string.tsecstr = true]; // 初始 JSON 内容
}

message CreateFilmSessionResponse {
  string session_id = 1;
  CommonRsp rsp = 2;
}

message SaveFilmSessionRequest {
  string session_id = 1[(validate.rules).string.tsecstr = true];
  string data = 2[(validate.rules).string.tsecstr = true];
  int32 version = 3;
}

message SaveFilmSessionResponse {
    CommonRsp rsp = 1;
}

message FilmSessionShareRequest{
    
}

message FilmSessionShareResponse{
    
}


message FilmSessionCopyRequest{
    repeated string file_session_list = 1; 
}

message FilmSessionCopyItem{
    string ori_film_session = 1;
    string new_film_session = 2;
}

message FilmSessionCopyResponse{
    repeated FilmSessionCopyItem items = 1;
    CommonRsp rsp = 2;
}


message SessionHistoryItem {
  string id = 1;
  string created_at = 2;
  string data = 3;
}

message GetFilmSessionHistoryRequest {
  string session_id = 1[(validate.rules).string.tsecstr = true];
}



message GetFilmSessionHistoryResponse {
  repeated SessionHistoryItem history = 1;
}


message Asset {
  string id = 1;
  repeated string tags = 2;
  string asset_type = 3;
  string data = 4;
  int32 state = 5; // 1: 有效, 2: 删除
  repeated string project_id_list = 6;
  int64 created_at = 7;
  string created_by = 8;
}

message GetAssetsListRequest{
    string project_id = 1[(validate.rules).string = {tsecstr : true,min_len:1}];
    string scene_id = 2[(validate.rules).string = {tsecstr : true,min_len:1}]; 
}

message GetAssetsListResponse{
    repeated Asset asset_list = 1;
    CommonRsp rsp = 2;
}

service BscCore { 
   // 项目
  rpc GetProjectList(GetProjectListRequest) returns (GetProjectListResponse);
  rpc GetProject(GetProjectRequest) returns (GetProjectResponse);
  rpc SearchProjectList(SearchProjectListRequest) returns (SearchProjectListResponse);
  rpc UpdateProject(UpdateProjectRequest) returns (UpdateProjectResponse);
  rpc DeleteProject(DeleteProjectRequest) returns (DeleteProjectResponse);
  rpc CreateProject(CreateProjectRequest) returns (CreateProjectResponse);    
  // 场景
  rpc GetSceneList(GetSceneListRequest) returns (GetSceneListResponse);
  rpc GetScene(GetSceneRequest) returns (GetSceneResponse);
  rpc SearchSceneList(SearchSceneListRequest) returns (SearchSceneListResponse);
  rpc UpdateScene(UpdateSceneRequest) returns (UpdateSceneResponse);
  rpc DeleteScene(DeleteSceneRequest) returns (DeleteSceneResponse);
  rpc CreateScene(CreateSceneRequest) returns (CreateSceneResponse);
  // 场次
  rpc GetFilmSessionList(GetFilmSessionListRequest) returns (GetFilmSessionListResponse);
  rpc GetFilmSession(GetFilmSessionRequest) returns (GetFilmSessionResponse);
  rpc SearchFilmSessionList(SearchFilmSessionListRequest) returns (SearchFilmSessionListResponse);
  rpc CreateFilmSession(CreateFilmSessionRequest) returns (CreateFilmSessionResponse);
  rpc SaveFilmSession(SaveFilmSessionRequest) returns (SaveFilmSessionResponse);
  rpc FilmSessionShare(FilmSessionShareRequest) returns(FilmSessionShareResponse);
  rpc FilmSessionCopy(FilmSessionCopyRequest) returns(FilmSessionCopyResponse);
  // 场次历史   
  rpc GetFilmSessionHistory(GetFilmSessionHistoryRequest) returns (GetFilmSessionHistoryResponse);
  
  // 资产相关
  rpc GetAssetsList(GetAssetsListRequest) returns (GetAssetsListResponse);

} 
